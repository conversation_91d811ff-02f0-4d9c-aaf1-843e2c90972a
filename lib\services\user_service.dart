import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';

class UserService {
  static const _storage = FlutterSecureStorage();
  static const String _userKey = 'current_user';
  static const String _emailKey = 'user_email';
  static const String _nameKey = 'user_name';

  // Get current user from secure storage
  Future<UserModel?> getCurrentUser() async {
    try {
      final email = await _storage.read(key: _emailKey);
      final name = await _storage.read(key: _nameKey);
      
      if (email != null) {
        return UserModel(
          id: email.hashCode.toString(),
          email: email,
          name: name ?? 'User',
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Login user (mock implementation)
  Future<UserModel> loginUser(String email, String password) async {
    // Mock validation - in real app, this would call an API
    if (email.isNotEmpty && password.isNotEmpty) {
      final user = UserModel(
        id: email.hashCode.toString(),
        email: email,
        name: 'User',
      );
      
      // Store user data securely
      await _storage.write(key: _emailKey, value: email);
      await _storage.write(key: _nameKey, value: 'User');
      
      return user;
    } else {
      throw Exception('Invalid credentials');
    }
  }

  // Register user (mock implementation)
  Future<UserModel> registerUser(String email, String password, {String? name}) async {
    // Mock registration - in real app, this would call an API
    if (email.isNotEmpty && password.isNotEmpty) {
      final user = UserModel(
        id: email.hashCode.toString(),
        email: email,
        name: name ?? 'User',
      );
      
      // Store user data securely
      await _storage.write(key: _emailKey, value: email);
      await _storage.write(key: _nameKey, value: name ?? 'User');
      
      return user;
    } else {
      throw Exception('Invalid registration data');
    }
  }

  // Update user profile
  Future<UserModel> updateUserProfile(String name, String? photoUrl) async {
    final email = await _storage.read(key: _emailKey);
    if (email != null) {
      await _storage.write(key: _nameKey, value: name);
      
      return UserModel(
        id: email.hashCode.toString(),
        email: email,
        name: name,
        photoUrl: photoUrl,
      );
    } else {
      throw Exception('User not found');
    }
  }

  // Logout user
  Future<void> logout() async {
    await _storage.delete(key: _emailKey);
    await _storage.delete(key: _nameKey);
    await _storage.delete(key: _userKey);
  }
}
